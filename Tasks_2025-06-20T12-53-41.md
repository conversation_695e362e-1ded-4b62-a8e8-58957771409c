[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:Dividend can be imported from CSV DESCRIPTION:Once imported show a table with all the transactions and option to edit and save all transactions
-[ ] NAME:Accounts page is not shown DESCRIPTION:Unable to add accounts, so unable to create investment transactions
-[ ] NAME:Account creation is not working DESCRIPTION:Don't need to show accounts inside investments
-[ ] NAME:Accounts is a seperate page, which handles all account creation - Bank, Credit card, Loan account, Investment account DESCRIPTION:Accounts page needs to shown as cards with balance in it
-[ ] NAME:Investments overview in old version looks good, in current version it looks ugly DESCRIPTION:
-[ ] NAME:Investment transactions import need to be allowed DESCRIPTION:CSV/XLSX files can be uploaded and transactions need to be extracted