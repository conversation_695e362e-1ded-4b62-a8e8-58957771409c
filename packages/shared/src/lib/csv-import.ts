import { supabase } from './supabase'
import type { IInvestmentTransaction, IAccount } from '../types'
import { InvestmentService } from './investments'

export interface CSVRow {
  [key: string]: string
}

export interface ParsedInvestmentTransaction {
  date: string
  symbol: string
  quantity: number
  price: number
  type: 'buy' | 'sell' | 'dividend'
  description?: string
  fees?: number
  amount?: number // For dividend transactions
}

export interface ColumnMapping {
  date: string
  symbol: string
  quantity: string
  price: string
  type: string
  description?: string
  fees?: string
  amount?: string // For dividend transactions
}

export interface ImportResult {
  success: number
  errors: Array<{ row: number; error: string; data: any }>
  transactions: IInvestmentTransaction[]
}

export class CSVImportService {
  /**
   * Parse CSV content into rows
   */
  static parseCSV(csvContent: string): CSVRow[] {
    const lines = csvContent.trim().split('\n')
    if (lines.length < 2) {
      throw new Error('CSV must have at least a header row and one data row')
    }

    const headers = this.parseCSVLine(lines[0])
    const rows: CSVRow[] = []

    for (let i = 1; i < lines.length; i++) {
      const values = this.parseCSVLine(lines[i])
      if (values.length === 0) continue // Skip empty lines

      const row: CSVRow = {}
      headers.forEach((header, index) => {
        row[header.trim()] = (values[index] || '').trim()
      })
      rows.push(row)
    }

    return rows
  }

  /**
   * Parse a single CSV line, handling quoted values
   */
  private static parseCSVLine(line: string): string[] {
    const result: string[] = []
    let current = ''
    let inQuotes = false
    let i = 0

    while (i < line.length) {
      const char = line[i]
      const nextChar = line[i + 1]

      if (char === '"') {
        if (inQuotes && nextChar === '"') {
          // Escaped quote
          current += '"'
          i += 2
        } else {
          // Toggle quote state
          inQuotes = !inQuotes
          i++
        }
      } else if (char === ',' && !inQuotes) {
        // End of field
        result.push(current)
        current = ''
        i++
      } else {
        current += char
        i++
      }
    }

    // Add the last field
    result.push(current)
    return result
  }

  /**
   * Get available column headers from CSV
   */
  static getCSVHeaders(csvContent: string): string[] {
    const lines = csvContent.trim().split('\n')
    if (lines.length === 0) {
      throw new Error('CSV is empty')
    }

    return this.parseCSVLine(lines[0]).map(header => header.trim())
  }

  /**
   * Auto-detect column mapping based on common header names
   */
  static autoDetectMapping(headers: string[]): Partial<ColumnMapping> {
    const mapping: Partial<ColumnMapping> = {}
    const lowerHeaders = headers.map(h => h.toLowerCase())

    // Date mapping
    const datePatterns = ['date', 'transaction_date', 'trade_date', 'settlement_date', 'timestamp']
    for (const pattern of datePatterns) {
      const index = lowerHeaders.findIndex(h => h.includes(pattern))
      if (index !== -1) {
        mapping.date = headers[index]
        break
      }
    }

    // Symbol mapping
    const symbolPatterns = ['symbol', 'ticker', 'stock', 'security', 'instrument']
    for (const pattern of symbolPatterns) {
      const index = lowerHeaders.findIndex(h => h.includes(pattern))
      if (index !== -1) {
        mapping.symbol = headers[index]
        break
      }
    }

    // Quantity mapping
    const quantityPatterns = ['quantity', 'shares', 'units', 'amount', 'qty']
    for (const pattern of quantityPatterns) {
      const index = lowerHeaders.findIndex(h => h.includes(pattern) && !h.includes('price'))
      if (index !== -1) {
        mapping.quantity = headers[index]
        break
      }
    }

    // Price mapping
    const pricePatterns = ['price', 'unit_price', 'share_price', 'cost', 'value']
    for (const pattern of pricePatterns) {
      const index = lowerHeaders.findIndex(h => h.includes(pattern))
      if (index !== -1) {
        mapping.price = headers[index]
        break
      }
    }

    // Type mapping
    const typePatterns = ['type', 'action', 'transaction_type', 'side', 'buy_sell']
    for (const pattern of typePatterns) {
      const index = lowerHeaders.findIndex(h => h.includes(pattern))
      if (index !== -1) {
        mapping.type = headers[index]
        break
      }
    }

    // Description mapping
    const descPatterns = ['description', 'memo', 'note', 'comment', 'details']
    for (const pattern of descPatterns) {
      const index = lowerHeaders.findIndex(h => h.includes(pattern))
      if (index !== -1) {
        mapping.description = headers[index]
        break
      }
    }

    // Fees mapping
    const feePatterns = ['fee', 'fees', 'commission', 'cost', 'charge']
    for (const pattern of feePatterns) {
      const index = lowerHeaders.findIndex(h => h.includes(pattern) && !h.includes('price'))
      if (index !== -1) {
        mapping.fees = headers[index]
        break
      }
    }

    // Amount mapping (for dividends)
    const amountPatterns = ['amount', 'dividend_amount', 'payment', 'cash', 'net_amount']
    for (const pattern of amountPatterns) {
      const index = lowerHeaders.findIndex(h => h.includes(pattern) && !h.includes('price') && !h.includes('quantity'))
      if (index !== -1) {
        mapping.amount = headers[index]
        break
      }
    }

    return mapping
  }

  /**
   * Parse CSV rows into investment transactions
   */
  static parseInvestmentTransactions(
    rows: CSVRow[],
    mapping: ColumnMapping
  ): ParsedInvestmentTransaction[] {
    const transactions: ParsedInvestmentTransaction[] = []

    for (const row of rows) {
      try {
        const transaction = this.parseInvestmentTransaction(row, mapping)
        transactions.push(transaction)
      } catch (error) {
        console.warn('Failed to parse row:', row, error)
        // Continue with other rows
      }
    }

    return transactions
  }

  /**
   * Parse a single row into an investment transaction
   */
  private static parseInvestmentTransaction(
    row: CSVRow,
    mapping: ColumnMapping
  ): ParsedInvestmentTransaction {
    // Parse date
    const dateStr = row[mapping.date]
    if (!dateStr) {
      throw new Error('Date is required')
    }
    const date = this.parseDate(dateStr)

    // Parse symbol
    const symbol = row[mapping.symbol]?.toUpperCase()
    if (!symbol) {
      throw new Error('Symbol is required')
    }

    // Parse quantity
    const quantityStr = row[mapping.quantity]
    if (!quantityStr) {
      throw new Error('Quantity is required')
    }
    const quantity = Math.abs(parseFloat(quantityStr.replace(/[^\d.-]/g, '')))
    if (isNaN(quantity) || quantity <= 0) {
      throw new Error('Invalid quantity')
    }

    // Parse price
    const priceStr = row[mapping.price]
    if (!priceStr) {
      throw new Error('Price is required')
    }
    const price = parseFloat(priceStr.replace(/[^\d.-]/g, ''))
    if (isNaN(price) || price <= 0) {
      throw new Error('Invalid price')
    }

    // Parse type
    const typeStr = row[mapping.type]?.toLowerCase()
    if (!typeStr) {
      throw new Error('Transaction type is required')
    }

    let type: 'buy' | 'sell' | 'dividend'
    if (typeStr.includes('buy') || typeStr.includes('purchase') || typeStr.includes('acquire')) {
      type = 'buy'
    } else if (typeStr.includes('sell') || typeStr.includes('sale') || typeStr.includes('dispose')) {
      type = 'sell'
    } else if (typeStr.includes('dividend') || typeStr.includes('div') || typeStr.includes('distribution')) {
      type = 'dividend'
    } else {
      throw new Error(`Unknown transaction type: ${typeStr}`)
    }

    // Parse optional fields
    const description = mapping.description ? row[mapping.description] : undefined
    const feesStr = mapping.fees ? row[mapping.fees] : undefined
    const fees = feesStr ? parseFloat(feesStr.replace(/[^\d.-]/g, '')) : 0

    // For dividend transactions, parse amount if available
    let amount: number | undefined
    if (type === 'dividend' && mapping.amount) {
      const amountStr = row[mapping.amount]
      if (amountStr) {
        amount = parseFloat(amountStr.replace(/[^\d.-]/g, ''))
        if (isNaN(amount)) {
          amount = undefined
        }
      }
    }

    return {
      date,
      symbol,
      quantity: type === 'dividend' ? 0 : quantity, // Dividends don't have quantity
      price: type === 'dividend' ? 0 : price, // Dividends don't have price
      type,
      description,
      fees: isNaN(fees) ? 0 : Math.abs(fees),
      amount: amount || (type === 'dividend' ? undefined : quantity * price)
    }
  }

  /**
   * Parse date string into ISO format
   */
  private static parseDate(dateStr: string): string {
    // Try different date formats
    const formats = [
      /^\d{4}-\d{2}-\d{2}$/, // YYYY-MM-DD
      /^\d{2}\/\d{2}\/\d{4}$/, // MM/DD/YYYY
      /^\d{2}-\d{2}-\d{4}$/, // MM-DD-YYYY
      /^\d{4}\/\d{2}\/\d{2}$/, // YYYY/MM/DD
    ]

    let date: Date

    if (formats[0].test(dateStr)) {
      // YYYY-MM-DD
      date = new Date(dateStr)
    } else if (formats[1].test(dateStr)) {
      // MM/DD/YYYY
      const [month, day, year] = dateStr.split('/')
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
    } else if (formats[2].test(dateStr)) {
      // MM-DD-YYYY
      const [month, day, year] = dateStr.split('-')
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
    } else if (formats[3].test(dateStr)) {
      // YYYY/MM/DD
      const [year, month, day] = dateStr.split('/')
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
    } else {
      // Try to parse as-is
      date = new Date(dateStr)
    }

    if (isNaN(date.getTime())) {
      throw new Error(`Invalid date format: ${dateStr}`)
    }

    return date.toISOString().split('T')[0]
  }

  /**
   * Import investment transactions from parsed data
   */
  static async importInvestmentTransactions(
    transactions: ParsedInvestmentTransaction[],
    accountId: string,
    fundingAccountId?: string // Required for dividend transactions
  ): Promise<ImportResult> {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      throw new Error('User not authenticated')
    }

    const result: ImportResult = {
      success: 0,
      errors: [],
      transactions: []
    }

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i]

      try {
        if (transaction.type === 'dividend') {
          // Handle dividend as a transfer
          if (!fundingAccountId) {
            throw new Error('Funding account is required for dividend transactions')
          }

          const { TransferService } = await import('./transfers')
          const dividendTransfer = await TransferService.createTransfer({
            amount: transaction.amount || 0,
            description: transaction.description || `Dividend payment: ${transaction.symbol}`,
            from_account_id: accountId,
            to_account_id: fundingAccountId,
            transaction_date: transaction.date,
            fees: transaction.fees || 0,
          })

          result.transactions.push(dividendTransfer as any)
          result.success++
        } else {
          // Handle buy/sell transactions
          const investmentTransaction = await InvestmentService.createInvestmentTransaction({
            amount: transaction.quantity * transaction.price,
            description: transaction.description || `${transaction.type.toUpperCase()} ${transaction.quantity} shares of ${transaction.symbol}`,
            account_id: accountId,
            investment_symbol: transaction.symbol,
            investment_quantity: transaction.quantity,
            investment_price: transaction.price,
            transaction_type: transaction.type === 'buy' ? 'investment_buy' : 'investment_sell',
            transaction_date: transaction.date,
            fees: transaction.fees || 0,
          })

          result.transactions.push(investmentTransaction)
          result.success++
        }
      } catch (error) {
        result.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: transaction
        })
      }
    }

    return result
  }
}
