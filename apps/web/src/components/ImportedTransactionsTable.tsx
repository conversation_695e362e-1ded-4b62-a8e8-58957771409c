import React, { useState } from 'react'
import { ParsedInvestmentTransaction } from '@shared/index'
import toast from 'react-hot-toast'

interface ImportedTransactionsTableProps {
  transactions: ParsedInvestmentTransaction[]
  onTransactionsChange: (transactions: ParsedInvestmentTransaction[]) => void
  onSave: () => Promise<void>
  loading?: boolean
}

export const ImportedTransactionsTable: React.FC<ImportedTransactionsTableProps> = ({
  transactions,
  onTransactionsChange,
  onSave,
  loading = false
}) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null)
  const [editingTransaction, setEditingTransaction] = useState<ParsedInvestmentTransaction | null>(null)

  const handleEdit = (index: number) => {
    setEditingIndex(index)
    setEditingTransaction({ ...transactions[index] })
  }

  const handleSaveEdit = () => {
    if (editingIndex !== null && editingTransaction) {
      const updatedTransactions = [...transactions]
      updatedTransactions[editingIndex] = editingTransaction
      onTransactionsChange(updatedTransactions)
      setEditingIndex(null)
      setEditingTransaction(null)
      toast.success('Transaction updated')
    }
  }

  const handleCancelEdit = () => {
    setEditingIndex(null)
    setEditingTransaction(null)
  }

  const handleDelete = (index: number) => {
    const updatedTransactions = transactions.filter((_, i) => i !== index)
    onTransactionsChange(updatedTransactions)
    toast.success('Transaction removed')
  }

  const handleFieldChange = (field: keyof ParsedInvestmentTransaction, value: any) => {
    if (editingTransaction) {
      setEditingTransaction({
        ...editingTransaction,
        [field]: value
      })
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'buy':
        return 'bg-green-100 text-green-800'
      case 'sell':
        return 'bg-red-100 text-red-800'
      case 'dividend':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-text-secondary">No transactions to display</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-text-primary">
          Imported Transactions ({transactions.length})
        </h3>
        <button
          onClick={onSave}
          disabled={loading}
          className="bg-primary-blue text-white px-4 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors disabled:opacity-50"
        >
          {loading ? 'Saving...' : 'Save All Transactions'}
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border border-border rounded-lg">
          <thead className="bg-surface-secondary">
            <tr>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Date</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Symbol</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Type</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Quantity</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Price</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Amount</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Fees</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Description</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-text-primary">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {transactions.map((transaction, index) => (
              <tr key={index} className="hover:bg-surface">
                {editingIndex === index ? (
                  // Edit mode
                  <>
                    <td className="px-4 py-3">
                      <input
                        type="date"
                        value={editingTransaction?.date || ''}
                        onChange={(e) => handleFieldChange('date', e.target.value)}
                        className="w-full px-2 py-1 border border-border rounded text-sm"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <input
                        type="text"
                        value={editingTransaction?.symbol || ''}
                        onChange={(e) => handleFieldChange('symbol', e.target.value.toUpperCase())}
                        className="w-full px-2 py-1 border border-border rounded text-sm"
                        placeholder="SYMBOL"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <select
                        value={editingTransaction?.type || ''}
                        onChange={(e) => handleFieldChange('type', e.target.value)}
                        className="w-full px-2 py-1 border border-border rounded text-sm"
                      >
                        <option value="buy">Buy</option>
                        <option value="sell">Sell</option>
                        <option value="dividend">Dividend</option>
                      </select>
                    </td>
                    <td className="px-4 py-3">
                      <input
                        type="number"
                        step="0.001"
                        value={editingTransaction?.quantity || ''}
                        onChange={(e) => handleFieldChange('quantity', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-border rounded text-sm"
                        disabled={editingTransaction?.type === 'dividend'}
                      />
                    </td>
                    <td className="px-4 py-3">
                      <input
                        type="number"
                        step="0.01"
                        value={editingTransaction?.price || ''}
                        onChange={(e) => handleFieldChange('price', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-border rounded text-sm"
                        disabled={editingTransaction?.type === 'dividend'}
                      />
                    </td>
                    <td className="px-4 py-3">
                      <input
                        type="number"
                        step="0.01"
                        value={editingTransaction?.amount || ''}
                        onChange={(e) => handleFieldChange('amount', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-border rounded text-sm"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <input
                        type="number"
                        step="0.01"
                        value={editingTransaction?.fees || ''}
                        onChange={(e) => handleFieldChange('fees', parseFloat(e.target.value) || 0)}
                        className="w-full px-2 py-1 border border-border rounded text-sm"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <input
                        type="text"
                        value={editingTransaction?.description || ''}
                        onChange={(e) => handleFieldChange('description', e.target.value)}
                        className="w-full px-2 py-1 border border-border rounded text-sm"
                        placeholder="Description"
                      />
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex space-x-2">
                        <button
                          onClick={handleSaveEdit}
                          className="text-green-600 hover:text-green-800 text-sm"
                        >
                          Save
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="text-gray-600 hover:text-gray-800 text-sm"
                        >
                          Cancel
                        </button>
                      </div>
                    </td>
                  </>
                ) : (
                  // View mode
                  <>
                    <td className="px-4 py-3 text-sm text-text-primary">
                      {new Date(transaction.date).toLocaleDateString()}
                    </td>
                    <td className="px-4 py-3 text-sm font-medium text-text-primary">
                      {transaction.symbol}
                    </td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTransactionTypeColor(transaction.type)}`}>
                        {transaction.type.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-text-primary">
                      {transaction.type === 'dividend' ? '-' : transaction.quantity.toFixed(3)}
                    </td>
                    <td className="px-4 py-3 text-sm text-text-primary">
                      {transaction.type === 'dividend' ? '-' : formatCurrency(transaction.price)}
                    </td>
                    <td className="px-4 py-3 text-sm text-text-primary font-medium">
                      {formatCurrency(transaction.amount || (transaction.quantity * transaction.price))}
                    </td>
                    <td className="px-4 py-3 text-sm text-text-primary">
                      {transaction.fees ? formatCurrency(transaction.fees) : '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-text-secondary">
                      {transaction.description || '-'}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(index)}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(index)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
