import React, { useState, useRef } from 'react'
import { CSVImportService, type ColumnMapping, type ParsedInvestmentTransaction, type ImportResult, AccountService, type IAccount } from '@repo/shared'
import { ImportedTransactionsTable } from './ImportedTransactionsTable'

export interface CSVImportProps {
  onImportComplete?: (result: ImportResult) => void
  onCancel?: () => void
  className?: string
}

export function CSVImport({ onImportComplete, onCancel, className = '' }: CSVImportProps) {
  const [file, setFile] = useState<File | null>(null)
  const [csvContent, setCsvContent] = useState('')
  const [headers, setHeaders] = useState<string[]>([])
  const [mapping, setMapping] = useState<Partial<ColumnMapping>>({})
  const [previewData, setPreviewData] = useState<ParsedInvestmentTransaction[]>([])
  const [allTransactions, setAllTransactions] = useState<ParsedInvestmentTransaction[]>([])
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [fundingAccounts, setFundingAccounts] = useState<IAccount[]>([])
  const [selectedAccountId, setSelectedAccountId] = useState('')
  const [selectedFundingAccountId, setSelectedFundingAccountId] = useState('')
  const [step, setStep] = useState<'upload' | 'mapping' | 'preview' | 'edit' | 'importing'>('upload')
  const [error, setError] = useState('')
  const [importing, setImporting] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  React.useEffect(() => {
    loadAccounts()
  }, [])

  const loadAccounts = async () => {
    try {
      const [investmentAccounts, allAccounts] = await Promise.all([
        AccountService.getAccounts({ account_type: 'investment' }),
        AccountService.getAccounts()
      ])

      setAccounts(investmentAccounts)
      setFundingAccounts(allAccounts.filter(acc => acc.account_type !== 'investment'))

      if (investmentAccounts.length > 0) {
        setSelectedAccountId(investmentAccounts[0].id)
      }
      if (allAccounts.filter(acc => acc.account_type !== 'investment').length > 0) {
        setSelectedFundingAccountId(allAccounts.filter(acc => acc.account_type !== 'investment')[0].id)
      }
    } catch (err) {
      console.error('Failed to load accounts:', err)
    }
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (!selectedFile) return

    // Validate file type
    const fileType = CSVImportService.detectFileType(selectedFile)
    if (fileType === 'unknown') {
      setError('Please select a CSV or XLSX file')
      return
    }

    if (selectedFile.size > 10 * 1024 * 1024) { // 10MB limit
      setError('File size must be less than 10MB')
      return
    }

    setFile(selectedFile)
    setError('')

    // Parse file content
    try {
      const { headers: fileHeaders, rows } = await CSVImportService.parseFile(selectedFile)
      setHeaders(fileHeaders)

      // Store the parsed content for later use
      if (fileType === 'csv') {
        const reader = new FileReader()
        reader.onload = (e) => {
          setCsvContent(e.target?.result as string)
        }
        reader.readAsText(selectedFile)
      } else {
        // For XLSX, we'll store the rows as CSV-like content
        const csvLikeContent = [
          fileHeaders.join(','),
          ...rows.map(row => fileHeaders.map(h => row[h] || '').join(','))
        ].join('\n')
        setCsvContent(csvLikeContent)
      }

      // Auto-detect mapping
      const autoMapping = CSVImportService.autoDetectMapping(fileHeaders)
      setMapping(autoMapping)

      setStep('mapping')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to parse file')
    }
  }

  const handleMappingChange = (field: keyof ColumnMapping, value: string) => {
    setMapping(prev => ({ ...prev, [field]: value }))
  }

  const validateMapping = (): boolean => {
    const required = ['date', 'symbol', 'type']
    for (const field of required) {
      if (!mapping[field as keyof ColumnMapping]) {
        setError(`${field} mapping is required`)
        return false
      }
    }
    return true
  }

  const handlePreview = () => {
    if (!validateMapping()) return

    try {
      const rows = CSVImportService.parseCSV(csvContent)
      const transactions = CSVImportService.parseInvestmentTransactions(rows, mapping as ColumnMapping)
      setAllTransactions(transactions)
      setPreviewData(transactions.slice(0, 10)) // Show first 10 for preview
      setStep('preview')
      setError('')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to parse transactions')
    }
  }

  const handleImport = async (transactionsToImport?: ParsedInvestmentTransaction[]) => {
    if (!selectedAccountId) {
      setError('Please select an investment account')
      return
    }

    const transactions = transactionsToImport || allTransactions
    const hasDividends = transactions.some(t => t.type === 'dividend')

    if (hasDividends && !selectedFundingAccountId) {
      setError('Please select a funding account for dividend transactions')
      return
    }

    setImporting(true)
    setStep('importing')

    try {
      const result = await CSVImportService.importInvestmentTransactions(
        transactions,
        selectedAccountId,
        selectedFundingAccountId
      )

      onImportComplete?.(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to import transactions')
      setStep('edit')
    } finally {
      setImporting(false)
    }
  }

  const renderUploadStep = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Upload CSV or Excel File</h3>
        <p className="text-gray-600 text-sm mb-4">
          Upload a CSV or Excel file containing your investment transactions. The file should include columns for date, symbol, quantity, price, and transaction type.
        </p>
      </div>

      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv,.xlsx,.xls"
          onChange={handleFileSelect}
          className="hidden"
        />
        <button
          onClick={() => fileInputRef.current?.click()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Select CSV or Excel File
        </button>
        {file && (
          <p className="mt-2 text-sm text-gray-600">
            Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
          </p>
        )}
      </div>
    </div>
  )

  const renderMappingStep = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Map CSV Columns</h3>
        <p className="text-gray-600 text-sm mb-4">
          Map your CSV columns to the required transaction fields. Required fields are marked with *.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[
          { key: 'date', label: 'Date *', required: true },
          { key: 'symbol', label: 'Symbol *', required: true },
          { key: 'quantity', label: 'Quantity (for buy/sell)', required: false },
          { key: 'price', label: 'Price (for buy/sell)', required: false },
          { key: 'type', label: 'Transaction Type *', required: true },
          { key: 'amount', label: 'Amount (for dividends)', required: false },
          { key: 'description', label: 'Description', required: false },
          { key: 'fees', label: 'Fees', required: false },
        ].map(({ key, label, required }) => (
          <div key={key}>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {label}
            </label>
            <select
              value={mapping[key as keyof ColumnMapping] || ''}
              onChange={(e) => handleMappingChange(key as keyof ColumnMapping, e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${
                required && !mapping[key as keyof ColumnMapping] 
                  ? 'border-red-300' 
                  : 'border-gray-300'
              }`}
            >
              <option value="">Select column...</option>
              {headers.map(header => (
                <option key={header} value={header}>{header}</option>
              ))}
            </select>
          </div>
        ))}
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setStep('upload')}
          className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Back
        </button>
        <button
          onClick={handlePreview}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Preview
        </button>
      </div>
    </div>
  )

  const renderPreviewStep = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Preview Transactions</h3>
        <p className="text-gray-600 text-sm mb-4">
          Review the first 10 transactions that will be imported. Make sure the data looks correct.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Investment Account *
          </label>
          <select
            value={selectedAccountId}
            onChange={(e) => setSelectedAccountId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">Select account...</option>
            {accounts.map(account => (
              <option key={account.id} value={account.id}>
                {account.name} ({account.account_type})
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Funding Account (for dividends)
          </label>
          <select
            value={selectedFundingAccountId}
            onChange={(e) => setSelectedFundingAccountId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">Select account...</option>
            {fundingAccounts.map(account => (
              <option key={account.id} value={account.id}>
                {account.name} ({account.account_type})
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Symbol</th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
              <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Fees</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {previewData.map((transaction, index) => (
              <tr key={index}>
                <td className="px-3 py-2 text-sm">{transaction.date}</td>
                <td className="px-3 py-2 text-sm font-medium">{transaction.symbol}</td>
                <td className="px-3 py-2 text-sm">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    transaction.type === 'buy'
                      ? 'bg-green-100 text-green-800'
                      : transaction.type === 'sell'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {transaction.type.toUpperCase()}
                  </span>
                </td>
                <td className="px-3 py-2 text-sm">{transaction.type === 'dividend' ? '-' : transaction.quantity}</td>
                <td className="px-3 py-2 text-sm">{transaction.type === 'dividend' ? '-' : `$${transaction.price.toFixed(2)}`}</td>
                <td className="px-3 py-2 text-sm">${(transaction.amount || (transaction.quantity * transaction.price)).toFixed(2)}</td>
                <td className="px-3 py-2 text-sm">${(transaction.fees || 0).toFixed(2)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex space-x-3">
        <button
          onClick={() => setStep('mapping')}
          className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Back
        </button>
        <button
          onClick={() => setStep('edit')}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Edit Transactions
        </button>
        <button
          onClick={() => handleImport()}
          disabled={!selectedAccountId}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          Import Transactions
        </button>
      </div>
    </div>
  )

  const renderEditStep = () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Edit Transactions</h3>
        <p className="text-gray-600 text-sm mb-4">
          Review and edit your transactions before importing. You can modify any field or remove transactions you don't want to import.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Investment Account *
          </label>
          <select
            value={selectedAccountId}
            onChange={(e) => setSelectedAccountId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">Select account...</option>
            {accounts.map(account => (
              <option key={account.id} value={account.id}>
                {account.name} ({account.account_type})
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Funding Account (for dividends)
          </label>
          <select
            value={selectedFundingAccountId}
            onChange={(e) => setSelectedFundingAccountId(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">Select account...</option>
            {fundingAccounts.map(account => (
              <option key={account.id} value={account.id}>
                {account.name} ({account.account_type})
              </option>
            ))}
          </select>
        </div>
      </div>

      <ImportedTransactionsTable
        transactions={allTransactions}
        onTransactionsChange={setAllTransactions}
        onSave={() => handleImport(allTransactions)}
        loading={importing}
      />

      <div className="flex space-x-3">
        <button
          onClick={() => setStep('preview')}
          className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Back to Preview
        </button>
      </div>
    </div>
  )

  const renderImportingStep = () => (
    <div className="text-center py-8">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <h3 className="text-lg font-semibold mb-2">Importing Transactions...</h3>
      <p className="text-gray-600">Please wait while we process your CSV file.</p>
    </div>
  )

  return (
    <div className={`max-w-4xl mx-auto p-6 ${className}`}>
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Import Investment Transactions</h2>
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          )}
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {step === 'upload' && renderUploadStep()}
        {step === 'mapping' && renderMappingStep()}
        {step === 'preview' && renderPreviewStep()}
        {step === 'edit' && renderEditStep()}
        {step === 'importing' && renderImportingStep()}
      </div>
    </div>
  )
}

export default CSVImport
