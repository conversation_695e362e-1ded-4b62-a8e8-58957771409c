'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  AccountService,
  InvestmentService,
  type IAccount,
  type IInvestmentTransaction
} from '@shared/index'
import { InvestmentForm } from '../../components/InvestmentForm'
import { CSVImport } from '../../components/CSVImport'
import { Modal } from '@/components/Modal'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import Navbar from '../../components/Navbar'

type TabType = 'overview' | 'transactions'

export default function InvestmentsPage() {
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const [investmentAccounts, setInvestmentAccounts] = useState<IAccount[]>([])
  const [investmentTransactions, setInvestmentTransactions] = useState<IInvestmentTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [showInvestmentForm, setShowInvestmentForm] = useState(false)
  const [showCSVImport, setShowCSVImport] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    loadInvestmentData()
  }, [refreshKey])

  const loadInvestmentData = async () => {
    try {
      setLoading(true)
      const [accounts, transactions] = await Promise.all([
        AccountService.getAccounts({ account_type: 'investment' }),
        InvestmentService.getInvestmentTransactions()
      ])
      setInvestmentAccounts(accounts)
      setInvestmentTransactions(transactions.data)
    } catch (error) {
      console.error('Failed to load investment data:', error)
      toast.error('Failed to load investment data')
    } finally {
      setLoading(false)
    }
  }

  const handleInvestmentSubmit = async (data: any) => {
    try {
      setSubmitting(true)
      await InvestmentService.createInvestmentTransaction(data)
      toast.success('Investment transaction created successfully!')
      setShowInvestmentForm(false)
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Error submitting investment transaction:', error)
      toast.error('Failed to submit investment transaction')
    } finally {
      setSubmitting(false)
    }
  }

  const handleCSVImportComplete = (result: any) => {
    if (result.success > 0) {
      toast.success(`Successfully imported ${result.success} transactions!`)
      setRefreshKey(prev => prev + 1)
    }
    if (result.errors.length > 0) {
      toast.error(`${result.errors.length} transactions failed to import`)
    }
    setShowCSVImport(false)
  }

  const calculatePortfolioValue = () => {
    return investmentAccounts.reduce((total, account) => total + (account.current_balance || 0), 0)
  }

  const calculateTotalInvested = () => {
    return investmentTransactions
      .filter(t => t.transaction_type === 'investment_buy')
      .reduce((total, t) => total + t.amount, 0)
  }

  const calculateTotalReturns = () => {
    const totalValue = calculatePortfolioValue()
    const totalInvested = calculateTotalInvested()
    return totalValue - totalInvested
  }

  const getReturnPercentage = () => {
    const totalInvested = calculateTotalInvested()
    if (totalInvested === 0) return 0
    return ((calculateTotalReturns() / totalInvested) * 100)
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navbar currentPage="investments" />
          <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-border border-t-primary-blue"></div>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar currentPage="investments" />

        <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
              <div>
                <h1 className="text-4xl font-bold text-text-primary">Investments</h1>
                <p className="text-text-secondary text-lg mt-2">Manage your investment portfolio and track performance</p>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowInvestmentForm(true)}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  Add Investment
                </button>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-surface-secondary rounded-lg p-1">
              {[
                { id: 'overview' as TabType, label: 'Overview', icon: '📊' },
                { id: 'transactions' as TabType, label: 'Transactions', icon: '📋' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-all ${
                    activeTab === tab.id
                      ? 'bg-primary-blue text-white shadow-sm'
                      : 'text-text-secondary hover:text-text-primary hover:bg-surface'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Overview Tab Content */}
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Portfolio Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-blue-600 text-sm font-medium uppercase tracking-wide">Portfolio Value</p>
                        <p className="text-3xl font-bold text-blue-900 mt-2">${calculatePortfolioValue().toFixed(2)}</p>
                        <p className="text-blue-600 text-xs mt-1">Total market value</p>
                      </div>
                      <div className="p-4 bg-blue-500 rounded-xl shadow-lg">
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-600 text-sm font-medium uppercase tracking-wide">Total Invested</p>
                        <p className="text-3xl font-bold text-green-900 mt-2">${calculateTotalInvested().toFixed(2)}</p>
                        <p className="text-green-600 text-xs mt-1">Capital deployed</p>
                      </div>
                      <div className="p-4 bg-green-500 rounded-xl shadow-lg">
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <div className={`bg-gradient-to-br ${calculateTotalReturns() >= 0 ? 'from-emerald-50 to-emerald-100 border-emerald-200' : 'from-red-50 to-red-100 border-red-200'} border rounded-xl p-6 hover:shadow-lg transition-all duration-300`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className={`${calculateTotalReturns() >= 0 ? 'text-emerald-600' : 'text-red-600'} text-sm font-medium uppercase tracking-wide`}>Total Returns</p>
                        <p className={`text-3xl font-bold mt-2 ${calculateTotalReturns() >= 0 ? 'text-emerald-900' : 'text-red-900'}`}>
                          {calculateTotalReturns() >= 0 ? '+' : ''}${calculateTotalReturns().toFixed(2)}
                        </p>
                        <p className={`${calculateTotalReturns() >= 0 ? 'text-emerald-600' : 'text-red-600'} text-xs mt-1`}>
                          {calculateTotalReturns() >= 0 ? 'Profit' : 'Loss'}
                        </p>
                      </div>
                      <div className={`p-4 rounded-xl shadow-lg ${calculateTotalReturns() >= 0 ? 'bg-emerald-500' : 'bg-red-500'}`}>
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={calculateTotalReturns() >= 0 ? "M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" : "M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"} />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <div className={`bg-gradient-to-br ${getReturnPercentage() >= 0 ? 'from-purple-50 to-purple-100 border-purple-200' : 'from-orange-50 to-orange-100 border-orange-200'} border rounded-xl p-6 hover:shadow-lg transition-all duration-300`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className={`${getReturnPercentage() >= 0 ? 'text-purple-600' : 'text-orange-600'} text-sm font-medium uppercase tracking-wide`}>Return %</p>
                        <p className={`text-3xl font-bold mt-2 ${getReturnPercentage() >= 0 ? 'text-purple-900' : 'text-orange-900'}`}>
                          {getReturnPercentage() >= 0 ? '+' : ''}{getReturnPercentage().toFixed(2)}%
                        </p>
                        <p className={`${getReturnPercentage() >= 0 ? 'text-purple-600' : 'text-orange-600'} text-xs mt-1`}>
                          Performance
                        </p>
                      </div>
                      <div className={`p-4 rounded-xl shadow-lg ${getReturnPercentage() >= 0 ? 'bg-purple-500' : 'bg-orange-500'}`}>
                        <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-surface border border-border rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-text-primary mb-4">Quick Actions</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                      onClick={() => setShowInvestmentForm(true)}
                      className="flex items-center justify-center gap-3 p-4 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add Transaction
                    </button>
                    <Link
                      href="/accounts"
                      className="flex items-center justify-center gap-3 p-4 bg-surface-secondary text-text-primary rounded-lg hover:bg-surface-secondary/80 transition-colors border border-border"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      Manage Accounts
                    </Link>
                    <button
                      onClick={() => setShowCSVImport(true)}
                      className="flex items-center justify-center gap-3 p-4 bg-surface-secondary text-text-primary rounded-lg hover:bg-surface-secondary/80 transition-colors border border-border"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                      </svg>
                      Import CSV
                    </button>
                  </div>
                </div>

                {/* Recent Transactions */}
                {investmentTransactions.length > 0 && (
                  <div className="bg-surface border border-border rounded-xl p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-lg font-semibold text-text-primary">Recent Transactions</h3>
                      <button
                        onClick={() => setActiveTab('transactions')}
                        className="text-primary-blue hover:text-primary-blue/80 text-sm font-medium"
                      >
                        View All →
                      </button>
                    </div>
                    <div className="space-y-3">
                      {investmentTransactions.slice(0, 5).map((transaction) => (
                        <div key={transaction.id} className="flex items-center justify-between p-4 bg-background rounded-lg border border-border hover:shadow-sm transition-shadow">
                          <div className="flex items-center gap-4">
                            <div className={`p-2 rounded-lg ${
                              transaction.transaction_type === 'investment_buy'
                                ? 'bg-green-100 text-green-600'
                                : transaction.transaction_type === 'investment_sell'
                                ? 'bg-red-100 text-red-600'
                                : 'bg-blue-100 text-blue-600'
                            }`}>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={
                                  transaction.transaction_type === 'investment_buy'
                                    ? "M7 11l5-5m0 0l5 5m-5-5v12"
                                    : transaction.transaction_type === 'investment_sell'
                                    ? "M17 13l-5 5m0 0l-5-5m5 5V6"
                                    : "M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                                } />
                              </svg>
                            </div>
                            <div>
                              <p className="font-medium text-text-primary">{transaction.investment_symbol}</p>
                              <p className="text-sm text-text-secondary">
                                {transaction.transaction_type === 'investment_buy' ? 'Buy' :
                                 transaction.transaction_type === 'investment_sell' ? 'Sell' : 'Dividend'} •
                                {new Date(transaction.transaction_date).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`font-bold ${
                              transaction.transaction_type === 'investment_buy'
                                ? 'text-red-600'
                                : 'text-green-600'
                            }`}>
                              {transaction.transaction_type === 'investment_buy' ? '-' : '+'}${transaction.amount.toFixed(2)}
                            </p>
                            {transaction.investment_quantity && (
                              <p className="text-sm text-text-secondary">
                                {transaction.investment_quantity} shares
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}



            {/* Transactions Tab Content */}
            {activeTab === 'transactions' && (
              <div className="space-y-6">
                <div className="bg-surface border border-border rounded-xl p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                    <h2 className="text-xl font-semibold text-text-primary">Investment Transactions</h2>
                    <div className="flex items-center gap-3">
                      <select className="px-3 py-2 border border-border rounded-lg bg-background text-text-primary text-sm">
                        <option value="">All Types</option>
                        <option value="investment_buy">Buy</option>
                        <option value="investment_sell">Sell</option>
                        <option value="dividend">Dividend</option>
                      </select>
                      <select className="px-3 py-2 border border-border rounded-lg bg-background text-text-primary text-sm">
                        <option value="">All Accounts</option>
                        {investmentAccounts.map((account) => (
                          <option key={account.id} value={account.id}>{account.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                  {investmentTransactions.length === 0 ? (
                    <div className="text-center py-12">
                      <svg className="w-16 h-16 text-text-secondary mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                      <h3 className="text-lg font-medium text-text-primary mb-2">No Transactions Found</h3>
                      <p className="text-text-secondary mb-4">Start by adding your first investment transaction.</p>
                      <button
                        onClick={() => setShowInvestmentForm(true)}
                        className="bg-primary-blue text-white px-6 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors"
                      >
                        Add Transaction
                      </button>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-border">
                            <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Date</th>
                            <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Type</th>
                            <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Symbol</th>
                            <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Account</th>
                            <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Quantity</th>
                            <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Price</th>
                            <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Amount</th>
                            <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {investmentTransactions.map((transaction) => (
                            <tr key={transaction.id} className="border-b border-border hover:bg-surface-elevated transition-colors">
                              <td className="py-4 px-4 text-text-primary">
                                <div className="flex flex-col">
                                  <span className="font-medium">{new Date(transaction.transaction_date).toLocaleDateString()}</span>
                                  <span className="text-xs text-text-secondary">{new Date(transaction.transaction_date).toLocaleTimeString()}</span>
                                </div>
                              </td>
                              <td className="py-4 px-4">
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                                  transaction.transaction_type === 'investment_buy'
                                    ? 'bg-green-100 text-green-800'
                                    : transaction.transaction_type === 'investment_sell'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-blue-100 text-blue-800'
                                }`}>
                                  {transaction.transaction_type === 'investment_buy' ? 'BUY' :
                                   transaction.transaction_type === 'investment_sell' ? 'SELL' : 'DIVIDEND'}
                                </span>
                              </td>
                              <td className="py-4 px-4">
                                <div className="flex flex-col">
                                  <span className="font-semibold text-text-primary">{transaction.investment_symbol || 'N/A'}</span>
                                  {transaction.description && (
                                    <span className="text-xs text-text-secondary truncate max-w-32">{transaction.description}</span>
                                  )}
                                </div>
                              </td>
                              <td className="py-4 px-4 text-text-primary">
                                <span className="text-sm">{transaction.account?.name || 'Unknown'}</span>
                              </td>
                              <td className="py-4 px-4 text-right text-text-primary font-medium">
                                {transaction.investment_quantity ? transaction.investment_quantity.toLocaleString() : 'N/A'}
                              </td>
                              <td className="py-4 px-4 text-right text-text-primary font-medium">
                                {transaction.investment_price ? `$${transaction.investment_price.toFixed(2)}` : 'N/A'}
                              </td>
                              <td className="py-4 px-4 text-right">
                                <span className={`font-bold ${
                                  transaction.transaction_type === 'investment_buy'
                                    ? 'text-red-600'
                                    : 'text-green-600'
                                }`}>
                                  {transaction.transaction_type === 'investment_buy' ? '-' : '+'}${transaction.amount.toFixed(2)}
                                </span>
                                {transaction.fees && transaction.fees > 0 && (
                                  <div className="text-xs text-text-secondary">Fee: ${transaction.fees.toFixed(2)}</div>
                                )}
                              </td>
                              <td className="py-4 px-4 text-right">
                                <div className="flex items-center justify-end gap-2">
                                  <button className="text-text-secondary hover:text-text-primary p-1 rounded transition-colors">
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                  </button>
                                  <button className="text-text-secondary hover:text-error-red p-1 rounded transition-colors">
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {investmentTransactions.length > 10 && (
                        <div className="mt-4 text-center">
                          <button className="text-primary-blue hover:text-primary-blue/80 font-medium text-sm">
                            View All Transactions ({investmentTransactions.length})
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Investment Form Modal */}
            <Modal
              isOpen={showInvestmentForm}
              onClose={() => setShowInvestmentForm(false)}
              title="Investment Transaction"
              size="xl"
            >
              <InvestmentForm
                onSubmit={handleInvestmentSubmit}
                loading={submitting}
                compact={true}
              />
            </Modal>

            {/* CSV Import Modal */}
            <Modal
              isOpen={showCSVImport}
              onClose={() => setShowCSVImport(false)}
              title="Import Investment Transactions"
              size="2xl"
            >
              <CSVImport
                onImportComplete={handleCSVImportComplete}
              />
            </Modal>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
