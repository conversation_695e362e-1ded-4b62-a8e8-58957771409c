'use client'

import { useState, useEffect } from 'react'
import {
  AccountService,
  InvestmentService,
  type IAccount,
  type IInvestmentTransaction
} from '@shared/index'
import { InvestmentForm } from '../../components/InvestmentForm'
import { Modal } from '@/components/Modal'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import Navbar from '../../components/Navbar'

type TabType = 'overview' | 'accounts' | 'transactions'

export default function InvestmentsPage() {
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const [investmentAccounts, setInvestmentAccounts] = useState<IAccount[]>([])
  const [investmentTransactions, setInvestmentTransactions] = useState<IInvestmentTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [showInvestmentForm, setShowInvestmentForm] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    loadInvestmentData()
  }, [refreshKey])

  const loadInvestmentData = async () => {
    try {
      setLoading(true)
      const [accounts, transactions] = await Promise.all([
        AccountService.getAccounts({ account_type: 'investment' }),
        InvestmentService.getInvestmentTransactions()
      ])
      setInvestmentAccounts(accounts)
      setInvestmentTransactions(transactions.data)
    } catch (error) {
      console.error('Failed to load investment data:', error)
      toast.error('Failed to load investment data')
    } finally {
      setLoading(false)
    }
  }

  const handleInvestmentSubmit = async (data: any) => {
    try {
      setSubmitting(true)
      toast.success('Investment transaction created successfully!')
      setShowInvestmentForm(false)
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Error submitting investment transaction:', error)
      toast.error('Failed to submit investment transaction')
    } finally {
      setSubmitting(false)
    }
  }

  const calculatePortfolioValue = () => {
    return investmentAccounts.reduce((total, account) => total + (account.current_balance || 0), 0)
  }

  const calculateTotalInvested = () => {
    return investmentTransactions
      .filter(t => t.transaction_type === 'investment_buy')
      .reduce((total, t) => total + t.amount, 0)
  }

  const calculateTotalReturns = () => {
    const totalValue = calculatePortfolioValue()
    const totalInvested = calculateTotalInvested()
    return totalValue - totalInvested
  }

  const getReturnPercentage = () => {
    const totalInvested = calculateTotalInvested()
    if (totalInvested === 0) return 0
    return ((calculateTotalReturns() / totalInvested) * 100)
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navbar currentPage="investments" />
          <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-border border-t-primary-blue"></div>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar currentPage="investments" />

        <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6">
              <div>
                <h1 className="text-4xl font-bold text-text-primary">Investments</h1>
                <p className="text-text-secondary text-lg mt-2">Manage your investment portfolio and track performance</p>
              </div>
              <div className="flex items-center gap-3">
                <button
                  onClick={() => setShowInvestmentForm(true)}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  Add Investment
                </button>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-surface-secondary rounded-lg p-1">
              {[
                { id: 'overview' as TabType, label: 'Overview', icon: '📊' },
                { id: 'accounts' as TabType, label: 'Accounts', icon: '🏦' },
                { id: 'transactions' as TabType, label: 'Transactions', icon: '📋' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-all ${
                    activeTab === tab.id
                      ? 'bg-primary-blue text-white shadow-sm'
                      : 'text-text-secondary hover:text-text-primary hover:bg-surface'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Overview Tab Content */}
            {activeTab === 'overview' && (
              <>
                {/* Portfolio Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-surface border border-border rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-text-secondary text-sm font-medium">Portfolio Value</p>
                    <p className="text-2xl font-bold text-text-primary">${calculatePortfolioValue().toFixed(2)}</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface border border-border rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-text-secondary text-sm font-medium">Total Invested</p>
                    <p className="text-2xl font-bold text-text-primary">${calculateTotalInvested().toFixed(2)}</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-lg">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface border border-border rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-text-secondary text-sm font-medium">Total Returns</p>
                    <p className={`text-2xl font-bold ${calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      ${calculateTotalReturns().toFixed(2)}
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${calculateTotalReturns() >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                    <svg className={`w-6 h-6 ${calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={calculateTotalReturns() >= 0 ? "M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" : "M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"} />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="bg-surface border border-border rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-text-secondary text-sm font-medium">Return %</p>
                    <p className={`text-2xl font-bold ${getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {getReturnPercentage().toFixed(2)}%
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${getReturnPercentage() >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                    <svg className={`w-6 h-6 ${getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
              </>
            )}

            {/* Accounts Tab Content */}
            {activeTab === 'accounts' && (
              <div className="space-y-6">
                <div className="bg-surface border border-border rounded-xl p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-text-primary">Investment Accounts</h2>
                    <button className="text-sm bg-primary-blue text-white px-4 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors">
                      Add Account
                    </button>
                  </div>
                  {investmentAccounts.length === 0 ? (
                    <div className="text-center py-12">
                      <svg className="w-16 h-16 text-text-secondary mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      <h3 className="text-lg font-medium text-text-primary mb-2">No Investment Accounts</h3>
                      <p className="text-text-secondary mb-4">Create your first investment account to start tracking your portfolio.</p>
                      <button className="bg-primary-blue text-white px-6 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors">
                        Create Account
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {investmentAccounts.map((account) => (
                        <div key={account.id} className="bg-background border border-border rounded-lg p-6 hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <h3 className="text-lg font-semibold text-text-primary">{account.name}</h3>
                                <span className="text-xs bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
                                  {account.account_type}
                                </span>
                                {account.is_primary && (
                                  <span className="text-xs bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
                                    Primary
                                  </span>
                                )}
                              </div>
                              {account.institution_name && (
                                <p className="text-sm text-text-secondary mb-3">{account.institution_name}</p>
                              )}
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div>
                                  <p className="text-xs text-text-secondary uppercase tracking-wide">Current Balance</p>
                                  <p className="text-xl font-bold text-text-primary">${account.current_balance?.toFixed(2) || '0.00'}</p>
                                </div>
                                <div>
                                  <p className="text-xs text-text-secondary uppercase tracking-wide">Account Number</p>
                                  <p className="text-sm text-text-primary font-mono">
                                    {account.account_number ? `****${account.account_number.slice(-4)}` : 'N/A'}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-xs text-text-secondary uppercase tracking-wide">Status</p>
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                    account.is_active
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {account.is_active ? 'Active' : 'Inactive'}
                                  </span>
                                </div>
                                <div>
                                  <p className="text-xs text-text-secondary uppercase tracking-wide">Created</p>
                                  <p className="text-sm text-text-primary">
                                    {new Date(account.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              {account.description && (
                                <div className="mt-3 pt-3 border-t border-border">
                                  <p className="text-sm text-text-secondary">{account.description}</p>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-2 ml-4">
                              <button className="text-text-secondary hover:text-text-primary p-2 rounded-lg hover:bg-surface transition-colors">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              <button className="text-text-secondary hover:text-error-red p-2 rounded-lg hover:bg-surface transition-colors">
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Transactions Tab Content */}
            {activeTab === 'transactions' && (
              <div className="space-y-6">
                <div className="bg-surface border border-border rounded-xl p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                    <h2 className="text-xl font-semibold text-text-primary">Investment Transactions</h2>
                    <div className="flex items-center gap-3">
                      <select className="px-3 py-2 border border-border rounded-lg bg-background text-text-primary text-sm">
                        <option value="">All Types</option>
                        <option value="investment_buy">Buy</option>
                        <option value="investment_sell">Sell</option>
                        <option value="dividend">Dividend</option>
                      </select>
                      <select className="px-3 py-2 border border-border rounded-lg bg-background text-text-primary text-sm">
                        <option value="">All Accounts</option>
                        {investmentAccounts.map((account) => (
                          <option key={account.id} value={account.id}>{account.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                  {investmentTransactions.length === 0 ? (
                    <div className="text-center py-12">
                      <svg className="w-16 h-16 text-text-secondary mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                      <h3 className="text-lg font-medium text-text-primary mb-2">No Transactions Found</h3>
                      <p className="text-text-secondary mb-4">Start by adding your first investment transaction.</p>
                      <button
                        onClick={() => setShowInvestmentForm(true)}
                        className="bg-primary-blue text-white px-6 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors"
                      >
                        Add Transaction
                      </button>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-border">
                            <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Date</th>
                            <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Type</th>
                            <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Symbol</th>
                            <th className="text-left py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Account</th>
                            <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Quantity</th>
                            <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Price</th>
                            <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Amount</th>
                            <th className="text-right py-4 px-4 font-semibold text-text-secondary uppercase tracking-wide text-xs">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {investmentTransactions.map((transaction) => (
                            <tr key={transaction.id} className="border-b border-border hover:bg-surface-elevated transition-colors">
                              <td className="py-4 px-4 text-text-primary">
                                <div className="flex flex-col">
                                  <span className="font-medium">{new Date(transaction.transaction_date).toLocaleDateString()}</span>
                                  <span className="text-xs text-text-secondary">{new Date(transaction.transaction_date).toLocaleTimeString()}</span>
                                </div>
                              </td>
                              <td className="py-4 px-4">
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                                  transaction.transaction_type === 'investment_buy'
                                    ? 'bg-green-100 text-green-800'
                                    : transaction.transaction_type === 'investment_sell'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-blue-100 text-blue-800'
                                }`}>
                                  {transaction.transaction_type === 'investment_buy' ? 'BUY' :
                                   transaction.transaction_type === 'investment_sell' ? 'SELL' : 'DIVIDEND'}
                                </span>
                              </td>
                              <td className="py-4 px-4">
                                <div className="flex flex-col">
                                  <span className="font-semibold text-text-primary">{transaction.investment_symbol || 'N/A'}</span>
                                  {transaction.description && (
                                    <span className="text-xs text-text-secondary truncate max-w-32">{transaction.description}</span>
                                  )}
                                </div>
                              </td>
                              <td className="py-4 px-4 text-text-primary">
                                <span className="text-sm">{transaction.account?.name || 'Unknown'}</span>
                              </td>
                              <td className="py-4 px-4 text-right text-text-primary font-medium">
                                {transaction.investment_quantity ? transaction.investment_quantity.toLocaleString() : 'N/A'}
                              </td>
                              <td className="py-4 px-4 text-right text-text-primary font-medium">
                                {transaction.investment_price ? `$${transaction.investment_price.toFixed(2)}` : 'N/A'}
                              </td>
                              <td className="py-4 px-4 text-right">
                                <span className={`font-bold ${
                                  transaction.transaction_type === 'investment_buy'
                                    ? 'text-red-600'
                                    : 'text-green-600'
                                }`}>
                                  {transaction.transaction_type === 'investment_buy' ? '-' : '+'}${transaction.amount.toFixed(2)}
                                </span>
                                {transaction.fees && transaction.fees > 0 && (
                                  <div className="text-xs text-text-secondary">Fee: ${transaction.fees.toFixed(2)}</div>
                                )}
                              </td>
                              <td className="py-4 px-4 text-right">
                                <div className="flex items-center justify-end gap-2">
                                  <button className="text-text-secondary hover:text-text-primary p-1 rounded transition-colors">
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                  </button>
                                  <button className="text-text-secondary hover:text-error-red p-1 rounded transition-colors">
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {investmentTransactions.length > 10 && (
                        <div className="mt-4 text-center">
                          <button className="text-primary-blue hover:text-primary-blue/80 font-medium text-sm">
                            View All Transactions ({investmentTransactions.length})
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Investment Form Modal */}
            <Modal
              isOpen={showInvestmentForm}
              onClose={() => setShowInvestmentForm(false)}
              title="Investment Transaction"
              size="xl"
            >
              <InvestmentForm
                onSubmit={handleInvestmentSubmit}
                loading={submitting}
                compact={true}
              />
            </Modal>
          </div>
        </main>
      </div>
    </ProtectedRoute>
  )
}
