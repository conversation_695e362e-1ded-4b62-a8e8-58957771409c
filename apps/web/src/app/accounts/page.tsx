'use client'

import { useState, useEffect } from 'react'
import {
  AccountService,
  type IAccount,
  type IAccountForm
} from '@shared/index'
import { Modal } from '@/components/Modal'
import { toast } from 'react-hot-toast'
import ProtectedRoute from '@/components/ProtectedRoute'
import Navbar from '../../components/Navbar'
import { AccountForm } from '@/components/AccountForm'

export default function AccountsPage() {
  const [accounts, setAccounts] = useState<IAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [showAccountForm, setShowAccountForm] = useState(false)
  const [editingAccount, setEditingAccount] = useState<IAccount | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  useEffect(() => {
    loadAccounts()
  }, [refreshKey])

  const loadAccounts = async () => {
    try {
      setLoading(true)
      const allAccounts = await AccountService.getAccounts()
      setAccounts(allAccounts)
    } catch (error) {
      console.error('Failed to load accounts:', error)
      toast.error('Failed to load accounts')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAccount = () => {
    setEditingAccount(null)
    setShowAccountForm(true)
  }

  const handleEditAccount = (account: IAccount) => {
    setEditingAccount(account)
    setShowAccountForm(true)
  }

  const handleAccountSubmit = async (data: IAccountForm) => {
    try {
      setSubmitting(true)
      
      if (editingAccount) {
        await AccountService.updateAccount(editingAccount.id, data)
        toast.success('Account updated successfully!')
      } else {
        await AccountService.createAccount(data)
        toast.success('Account created successfully!')
      }
      
      setShowAccountForm(false)
      setEditingAccount(null)
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Failed to save account:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to save account')
    } finally {
      setSubmitting(false)
    }
  }

  const handleDeleteAccount = async (account: IAccount) => {
    if (!confirm(`Are you sure you want to delete "${account.name}"?`)) {
      return
    }

    try {
      await AccountService.deleteAccount(account.id)
      toast.success('Account deleted successfully!')
      setRefreshKey(prev => prev + 1)
    } catch (error) {
      console.error('Failed to delete account:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete account')
    }
  }

  const getAccountTypeIcon = (type: string) => {
    switch (type) {
      case 'bank':
        return '🏦'
      case 'investment':
        return '📈'
      case 'savings':
        return '💰'
      case 'credit_card':
        return '💳'
      case 'cash':
        return '💵'
      default:
        return '🏛️'
    }
  }

  const getAccountTypeColor = (type: string) => {
    switch (type) {
      case 'bank':
        return 'bg-blue-100 text-blue-800'
      case 'investment':
        return 'bg-green-100 text-green-800'
      case 'savings':
        return 'bg-purple-100 text-purple-800'
      case 'credit_card':
        return 'bg-red-100 text-red-800'
      case 'cash':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const groupedAccounts = accounts.reduce((groups, account) => {
    const type = account.account_type
    if (!groups[type]) {
      groups[type] = []
    }
    groups[type].push(account)
    return groups
  }, {} as Record<string, IAccount[]>)

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background">
          <Navbar currentPage="accounts" />
          <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-4 border-border border-t-primary-blue"></div>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <Navbar currentPage="accounts" />

        <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* Page Header */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-4xl font-bold text-text-primary">Accounts</h1>
                <p className="text-text-secondary text-lg mt-2">Manage your financial accounts</p>
              </div>
              <button
                onClick={handleCreateAccount}
                className="bg-primary-blue text-white px-6 py-3 rounded-lg hover:bg-primary-blue/90 transition-colors font-medium"
              >
                Add Account
              </button>
            </div>

            {/* Accounts Grid */}
            {Object.keys(groupedAccounts).length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-surface-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🏦</span>
                </div>
                <h3 className="text-lg font-medium text-text-primary mb-2">No Accounts Yet</h3>
                <p className="text-text-secondary mb-4">Create your first account to start tracking your finances.</p>
                <button
                  onClick={handleCreateAccount}
                  className="bg-primary-blue text-white px-6 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors"
                >
                  Create Account
                </button>
              </div>
            ) : (
              <div className="space-y-8">
                {Object.entries(groupedAccounts).map(([type, typeAccounts]) => (
                  <div key={type} className="space-y-4">
                    <h2 className="text-xl font-semibold text-text-primary capitalize flex items-center gap-2">
                      <span>{getAccountTypeIcon(type)}</span>
                      {type.replace('_', ' ')} Accounts
                      <span className="text-sm font-normal text-text-secondary">({typeAccounts.length})</span>
                    </h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {typeAccounts.map((account) => (
                        <div
                          key={account.id}
                          className="bg-surface border border-border rounded-xl p-6 hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-surface-secondary rounded-lg flex items-center justify-center">
                                <span className="text-lg">{getAccountTypeIcon(account.account_type)}</span>
                              </div>
                              <div>
                                <h3 className="font-semibold text-text-primary">{account.name}</h3>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getAccountTypeColor(account.account_type)}`}>
                                  {account.account_type.replace('_', ' ')}
                                </span>
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => handleEditAccount(account)}
                                className="text-text-secondary hover:text-primary-blue transition-colors"
                                title="Edit account"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              <button
                                onClick={() => handleDeleteAccount(account)}
                                className="text-text-secondary hover:text-error-red transition-colors"
                                title="Delete account"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>

                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-text-secondary">Current Balance</span>
                              <span className="text-lg font-bold text-text-primary">
                                {formatCurrency(account.current_balance || 0)}
                              </span>
                            </div>
                            
                            {account.institution_name && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-text-secondary">Institution</span>
                                <span className="text-sm text-text-primary">{account.institution_name}</span>
                              </div>
                            )}
                            
                            {account.account_number && (
                              <div className="flex justify-between items-center">
                                <span className="text-sm text-text-secondary">Account Number</span>
                                <span className="text-sm text-text-primary font-mono">
                                  ****{account.account_number.slice(-4)}
                                </span>
                              </div>
                            )}
                            
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-text-secondary">Status</span>
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                account.is_active
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {account.is_active ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Account Form Modal */}
          <Modal
            isOpen={showAccountForm}
            onClose={() => {
              setShowAccountForm(false)
              setEditingAccount(null)
            }}
            title={editingAccount ? 'Edit Account' : 'Create Account'}
            size="lg"
          >
            <AccountForm
              onSubmit={handleAccountSubmit}
              loading={submitting}
              initialData={editingAccount}
            />
          </Modal>
        </main>
      </div>
    </ProtectedRoute>
  )
}
