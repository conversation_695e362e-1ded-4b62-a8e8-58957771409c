"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/investments/page",{

/***/ "(app-pages-browser)/./src/app/investments/page.tsx":
/*!**************************************!*\
  !*** ./src/app/investments/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvestmentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @shared/index */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _components_InvestmentForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/InvestmentForm */ \"(app-pages-browser)/./src/components/InvestmentForm.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction InvestmentsPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [investmentAccounts, setInvestmentAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [investmentTransactions, setInvestmentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showInvestmentForm, setShowInvestmentForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvestmentsPage.useEffect\": ()=>{\n            loadInvestmentData();\n        }\n    }[\"InvestmentsPage.useEffect\"], [\n        refreshKey\n    ]);\n    const loadInvestmentData = async ()=>{\n        try {\n            setLoading(true);\n            const [accounts, transactions] = await Promise.all([\n                _shared_index__WEBPACK_IMPORTED_MODULE_2__.AccountService.getAccounts({\n                    account_type: 'investment'\n                }),\n                _shared_index__WEBPACK_IMPORTED_MODULE_2__.InvestmentService.getInvestmentTransactions()\n            ]);\n            setInvestmentAccounts(accounts);\n            setInvestmentTransactions(transactions.data);\n        } catch (error) {\n            console.error('Failed to load investment data:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to load investment data');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInvestmentSubmit = async (data)=>{\n        try {\n            setSubmitting(true);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.success('Investment transaction created successfully!');\n            setShowInvestmentForm(false);\n            setRefreshKey((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Error submitting investment transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to submit investment transaction');\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const calculatePortfolioValue = ()=>{\n        return investmentAccounts.reduce((total, account)=>total + (account.current_balance || 0), 0);\n    };\n    const calculateTotalInvested = ()=>{\n        return investmentTransactions.filter((t)=>t.transaction_type === 'investment_buy').reduce((total, t)=>total + t.amount, 0);\n    };\n    const calculateTotalReturns = ()=>{\n        const totalValue = calculatePortfolioValue();\n        const totalInvested = calculateTotalInvested();\n        return totalValue - totalInvested;\n    };\n    const getReturnPercentage = ()=>{\n        const totalInvested = calculateTotalInvested();\n        if (totalInvested === 0) return 0;\n        return calculateTotalReturns() / totalInvested * 100;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-background\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        currentPage: \"investments\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-4 border-border border-t-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    currentPage: \"investments\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-text-primary\",\n                                                children: \"Investments\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary text-lg mt-2\",\n                                                children: \"Manage your investment portfolio and track performance\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowInvestmentForm(true),\n                                            className: \"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Investment\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-surface-secondary rounded-lg p-1\",\n                                children: [\n                                    {\n                                        id: 'overview',\n                                        label: 'Overview',\n                                        icon: '📊'\n                                    },\n                                    {\n                                        id: 'accounts',\n                                        label: 'Accounts',\n                                        icon: '🏦'\n                                    },\n                                    {\n                                        id: 'transactions',\n                                        label: 'Transactions',\n                                        icon: '📋'\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-all \".concat(activeTab === tab.id ? 'bg-primary-blue text-white shadow-sm' : 'text-text-secondary hover:text-text-primary hover:bg-surface'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-surface border border-border rounded-xl p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-text-secondary text-sm font-medium\",\n                                                                children: \"Portfolio Value\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-text-primary\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    calculatePortfolioValue().toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-100 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 text-blue-600\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-surface border border-border rounded-xl p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-text-secondary text-sm font-medium\",\n                                                                children: \"Total Invested\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-text-primary\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    calculateTotalInvested().toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-green-100 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 text-green-600\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M7 11l5-5m0 0l5 5m-5-5v12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-surface border border-border rounded-xl p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-text-secondary text-sm font-medium\",\n                                                                children: \"Total Returns\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold \".concat(calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                                children: [\n                                                                    \"$\",\n                                                                    calculateTotalReturns().toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg \".concat(calculateTotalReturns() >= 0 ? 'bg-green-100' : 'bg-red-100'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 \".concat(calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: calculateTotalReturns() >= 0 ? \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" : \"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-surface border border-border rounded-xl p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-text-secondary text-sm font-medium\",\n                                                                children: \"Return %\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold \".concat(getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                                children: [\n                                                                    getReturnPercentage().toFixed(2),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg \".concat(getReturnPercentage() >= 0 ? 'bg-green-100' : 'bg-red-100'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 \".concat(getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false),\n                            activeTab === 'accounts' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-surface border border-border rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-text-primary\",\n                                                    children: \"Investment Accounts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-sm bg-primary-blue text-white px-4 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                                    children: \"Add Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 19\n                                        }, this),\n                                        investmentAccounts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-16 h-16 text-text-secondary mx-auto mb-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-text-primary mb-2\",\n                                                    children: \"No Investment Accounts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-text-secondary mb-4\",\n                                                    children: \"Create your first investment account to start tracking your portfolio.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-primary-blue text-white px-6 py-2 rounded-lg hover:bg-primary-blue/90 transition-colors\",\n                                                    children: \"Create Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: investmentAccounts.map((account)=>{\n                                                var _account_current_balance;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-background border border-border rounded-lg p-6 hover:shadow-md transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-3 mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-lg font-semibold text-text-primary\",\n                                                                                children: account.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 244,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium\",\n                                                                                children: account.account_type\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 245,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            account.is_primary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium\",\n                                                                                children: \"Primary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 249,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    account.institution_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-text-secondary mb-3\",\n                                                                        children: account.institution_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 33\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-text-secondary uppercase tracking-wide\",\n                                                                                        children: \"Current Balance\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                        lineNumber: 259,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xl font-bold text-text-primary\",\n                                                                                        children: [\n                                                                                            \"$\",\n                                                                                            ((_account_current_balance = account.current_balance) === null || _account_current_balance === void 0 ? void 0 : _account_current_balance.toFixed(2)) || '0.00'\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                        lineNumber: 260,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 258,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-text-secondary uppercase tracking-wide\",\n                                                                                        children: \"Account Number\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                        lineNumber: 263,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-text-primary font-mono\",\n                                                                                        children: account.account_number ? \"****\".concat(account.account_number.slice(-4)) : 'N/A'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-text-secondary uppercase tracking-wide\",\n                                                                                        children: \"Status\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                        lineNumber: 269,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(account.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                                        children: account.is_active ? 'Active' : 'Inactive'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                        lineNumber: 270,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-text-secondary uppercase tracking-wide\",\n                                                                                        children: \"Created\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                        lineNumber: 279,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-text-primary\",\n                                                                                        children: new Date(account.created_at).toLocaleDateString()\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                        lineNumber: 280,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 278,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    account.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-3 pt-3 border-t border-border\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-text-secondary\",\n                                                                            children: account.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-text-secondary hover:text-text-primary p-2 rounded-lg hover:bg-surface transition-colors\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-text-secondary hover:text-error-red p-2 rounded-lg hover:bg-surface transition-colors\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, account.id, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 25\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === 'transactions' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface border border-border rounded-xl p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                                        children: \"Recent Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    investmentTransactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 text-text-secondary mx-auto mb-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary\",\n                                                children: \"No investment transactions found.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Symbol\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Price\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: investmentTransactions.slice(0, 10).map((transaction)=>{\n                                                        var _transaction_investment_price;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-border hover:bg-surface-elevated\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: new Date(transaction.transaction_date).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(transaction.transaction_type === 'investment_buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                        children: transaction.transaction_type === 'investment_buy' ? 'Buy' : 'Sell'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary font-medium\",\n                                                                    children: transaction.investment_symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: transaction.investment_quantity\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        (_transaction_investment_price = transaction.investment_price) === null || _transaction_investment_price === void 0 ? void 0 : _transaction_investment_price.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary font-medium\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        transaction.amount.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, transaction.id, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                                isOpen: showInvestmentForm,\n                                onClose: ()=>setShowInvestmentForm(false),\n                                title: \"Investment Transaction\",\n                                size: \"xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvestmentForm__WEBPACK_IMPORTED_MODULE_3__.InvestmentForm, {\n                                    onSubmit: handleInvestmentSubmit,\n                                    loading: submitting,\n                                    compact: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(InvestmentsPage, \"GbZkdDSwNIkUe2WvjyQA6Sg22P8=\");\n_c = InvestmentsPage;\nvar _c;\n$RefreshReg$(_c, \"InvestmentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/investments/page.tsx\n"));

/***/ })

});