"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/investments/page",{

/***/ "(app-pages-browser)/./src/app/investments/page.tsx":
/*!**************************************!*\
  !*** ./src/app/investments/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvestmentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @shared/index */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _components_InvestmentForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/InvestmentForm */ \"(app-pages-browser)/./src/components/InvestmentForm.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction InvestmentsPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [investmentAccounts, setInvestmentAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [investmentTransactions, setInvestmentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showInvestmentForm, setShowInvestmentForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvestmentsPage.useEffect\": ()=>{\n            loadInvestmentData();\n        }\n    }[\"InvestmentsPage.useEffect\"], [\n        refreshKey\n    ]);\n    const loadInvestmentData = async ()=>{\n        try {\n            setLoading(true);\n            const [accounts, transactions] = await Promise.all([\n                _shared_index__WEBPACK_IMPORTED_MODULE_2__.AccountService.getAccounts({\n                    account_type: 'investment'\n                }),\n                _shared_index__WEBPACK_IMPORTED_MODULE_2__.InvestmentService.getInvestmentTransactions()\n            ]);\n            setInvestmentAccounts(accounts);\n            setInvestmentTransactions(transactions.data);\n        } catch (error) {\n            console.error('Failed to load investment data:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to load investment data');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInvestmentSubmit = async (data)=>{\n        try {\n            setSubmitting(true);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.success('Investment transaction created successfully!');\n            setShowInvestmentForm(false);\n            setRefreshKey((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Error submitting investment transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to submit investment transaction');\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const calculatePortfolioValue = ()=>{\n        return investmentAccounts.reduce((total, account)=>total + (account.current_balance || 0), 0);\n    };\n    const calculateTotalInvested = ()=>{\n        return investmentTransactions.filter((t)=>t.transaction_type === 'investment_buy').reduce((total, t)=>total + t.amount, 0);\n    };\n    const calculateTotalReturns = ()=>{\n        const totalValue = calculatePortfolioValue();\n        const totalInvested = calculateTotalInvested();\n        return totalValue - totalInvested;\n    };\n    const getReturnPercentage = ()=>{\n        const totalInvested = calculateTotalInvested();\n        if (totalInvested === 0) return 0;\n        return calculateTotalReturns() / totalInvested * 100;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-background\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        currentPage: \"investments\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-4 border-border border-t-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    currentPage: \"investments\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-text-primary\",\n                                                children: \"Investments\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary text-lg mt-2\",\n                                                children: \"Manage your investment portfolio and track performance\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowInvestmentForm(true),\n                                            className: \"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Investment\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface border border-border rounded-xl p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-text-secondary text-sm font-medium\",\n                                                            children: \"Portfolio Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-text-primary\",\n                                                            children: [\n                                                                \"$\",\n                                                                calculatePortfolioValue().toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-100 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-blue-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface border border-border rounded-xl p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-text-secondary text-sm font-medium\",\n                                                            children: \"Total Invested\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-text-primary\",\n                                                            children: [\n                                                                \"$\",\n                                                                calculateTotalInvested().toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-green-100 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-green-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M7 11l5-5m0 0l5 5m-5-5v12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface border border-border rounded-xl p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-text-secondary text-sm font-medium\",\n                                                            children: \"Total Returns\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold \".concat(calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                            children: [\n                                                                \"$\",\n                                                                calculateTotalReturns().toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(calculateTotalReturns() >= 0 ? 'bg-green-100' : 'bg-red-100'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 \".concat(calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: calculateTotalReturns() >= 0 ? \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" : \"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface border border-border rounded-xl p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-text-secondary text-sm font-medium\",\n                                                            children: \"Return %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold \".concat(getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                            children: [\n                                                                getReturnPercentage().toFixed(2),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(getReturnPercentage() >= 0 ? 'bg-green-100' : 'bg-red-100'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 \".concat(getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface border border-border rounded-xl p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                                        children: \"Investment Accounts\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this),\n                                    investmentAccounts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 text-text-secondary mx-auto mb-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary\",\n                                                children: \"No investment accounts found. Create one to get started.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: investmentAccounts.map((account)=>{\n                                            var _account_current_balance;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-background border border-border rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-text-primary\",\n                                                                children: account.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\",\n                                                                children: account.account_type\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-text-primary\",\n                                                        children: [\n                                                            \"$\",\n                                                            ((_account_current_balance = account.current_balance) === null || _account_current_balance === void 0 ? void 0 : _account_current_balance.toFixed(2)) || '0.00'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    account.institution_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-text-secondary mt-1\",\n                                                        children: account.institution_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, account.id, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface border border-border rounded-xl p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                                        children: \"Recent Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this),\n                                    investmentTransactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 text-text-secondary mx-auto mb-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary\",\n                                                children: \"No investment transactions found.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Symbol\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Price\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: investmentTransactions.slice(0, 10).map((transaction)=>{\n                                                        var _transaction_investment_price;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-border hover:bg-surface-elevated\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: new Date(transaction.transaction_date).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(transaction.transaction_type === 'investment_buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                        children: transaction.transaction_type === 'investment_buy' ? 'Buy' : 'Sell'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary font-medium\",\n                                                                    children: transaction.investment_symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: transaction.investment_quantity\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        (_transaction_investment_price = transaction.investment_price) === null || _transaction_investment_price === void 0 ? void 0 : _transaction_investment_price.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary font-medium\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        transaction.amount.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, transaction.id, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                                isOpen: showInvestmentForm,\n                                onClose: ()=>setShowInvestmentForm(false),\n                                title: \"Investment Transaction\",\n                                size: \"xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvestmentForm__WEBPACK_IMPORTED_MODULE_3__.InvestmentForm, {\n                                    onSubmit: handleInvestmentSubmit,\n                                    loading: submitting,\n                                    compact: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(InvestmentsPage, \"GbZkdDSwNIkUe2WvjyQA6Sg22P8=\");\n_c = InvestmentsPage;\nvar _c;\n$RefreshReg$(_c, \"InvestmentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/investments/page.tsx\n"));

/***/ })

});