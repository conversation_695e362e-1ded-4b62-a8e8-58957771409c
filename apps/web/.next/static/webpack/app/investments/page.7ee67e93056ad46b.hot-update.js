"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/investments/page",{

/***/ "(app-pages-browser)/./src/app/investments/page.tsx":
/*!**************************************!*\
  !*** ./src/app/investments/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvestmentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @shared/index */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _components_InvestmentForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/InvestmentForm */ \"(app-pages-browser)/./src/components/InvestmentForm.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Modal */ \"(app-pages-browser)/./src/components/Modal.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/Navbar */ \"(app-pages-browser)/./src/components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction InvestmentsPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const [investmentAccounts, setInvestmentAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [investmentTransactions, setInvestmentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showInvestmentForm, setShowInvestmentForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvestmentsPage.useEffect\": ()=>{\n            loadInvestmentData();\n        }\n    }[\"InvestmentsPage.useEffect\"], [\n        refreshKey\n    ]);\n    const loadInvestmentData = async ()=>{\n        try {\n            setLoading(true);\n            const [accounts, transactions] = await Promise.all([\n                _shared_index__WEBPACK_IMPORTED_MODULE_2__.AccountService.getAccounts({\n                    account_type: 'investment'\n                }),\n                _shared_index__WEBPACK_IMPORTED_MODULE_2__.InvestmentService.getInvestmentTransactions()\n            ]);\n            setInvestmentAccounts(accounts);\n            setInvestmentTransactions(transactions.data);\n        } catch (error) {\n            console.error('Failed to load investment data:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to load investment data');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInvestmentSubmit = async (data)=>{\n        try {\n            setSubmitting(true);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.success('Investment transaction created successfully!');\n            setShowInvestmentForm(false);\n            setRefreshKey((prev)=>prev + 1);\n        } catch (error) {\n            console.error('Error submitting investment transaction:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to submit investment transaction');\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const calculatePortfolioValue = ()=>{\n        return investmentAccounts.reduce((total, account)=>total + (account.current_balance || 0), 0);\n    };\n    const calculateTotalInvested = ()=>{\n        return investmentTransactions.filter((t)=>t.transaction_type === 'investment_buy').reduce((total, t)=>total + t.amount, 0);\n    };\n    const calculateTotalReturns = ()=>{\n        const totalValue = calculatePortfolioValue();\n        const totalInvested = calculateTotalInvested();\n        return totalValue - totalInvested;\n    };\n    const getReturnPercentage = ()=>{\n        const totalInvested = calculateTotalInvested();\n        if (totalInvested === 0) return 0;\n        return calculateTotalReturns() / totalInvested * 100;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-background\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        currentPage: \"investments\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-12 w-12 border-4 border-border border-t-primary-blue\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    currentPage: \"investments\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-text-primary\",\n                                                children: \"Investments\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary text-lg mt-2\",\n                                                children: \"Manage your investment portfolio and track performance\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowInvestmentForm(true),\n                                            className: \"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Investment\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 bg-surface-secondary rounded-lg p-1\",\n                                children: [\n                                    {\n                                        id: 'overview',\n                                        label: 'Overview',\n                                        icon: '📊'\n                                    },\n                                    {\n                                        id: 'accounts',\n                                        label: 'Accounts',\n                                        icon: '🏦'\n                                    },\n                                    {\n                                        id: 'transactions',\n                                        label: 'Transactions',\n                                        icon: '📋'\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-all \".concat(activeTab === tab.id ? 'bg-primary-blue text-white shadow-sm' : 'text-text-secondary hover:text-text-primary hover:bg-surface'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.label\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface border border-border rounded-xl p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-text-secondary text-sm font-medium\",\n                                                            children: \"Portfolio Value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-text-primary\",\n                                                            children: [\n                                                                \"$\",\n                                                                calculatePortfolioValue().toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-blue-100 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-blue-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface border border-border rounded-xl p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-text-secondary text-sm font-medium\",\n                                                            children: \"Total Invested\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-text-primary\",\n                                                            children: [\n                                                                \"$\",\n                                                                calculateTotalInvested().toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 bg-green-100 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 text-green-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M7 11l5-5m0 0l5 5m-5-5v12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface border border-border rounded-xl p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-text-secondary text-sm font-medium\",\n                                                            children: \"Total Returns\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold \".concat(calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                            children: [\n                                                                \"$\",\n                                                                calculateTotalReturns().toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(calculateTotalReturns() >= 0 ? 'bg-green-100' : 'bg-red-100'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 \".concat(calculateTotalReturns() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: calculateTotalReturns() >= 0 ? \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" : \"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-surface border border-border rounded-xl p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-text-secondary text-sm font-medium\",\n                                                            children: \"Return %\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold \".concat(getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                            children: [\n                                                                getReturnPercentage().toFixed(2),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg \".concat(getReturnPercentage() >= 0 ? 'bg-green-100' : 'bg-red-100'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6 \".concat(getReturnPercentage() >= 0 ? 'text-green-600' : 'text-red-600'),\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface border border-border rounded-xl p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                                        children: \"Investment Accounts\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this),\n                                    investmentAccounts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 text-text-secondary mx-auto mb-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary\",\n                                                children: \"No investment accounts found. Create one to get started.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                        children: investmentAccounts.map((account)=>{\n                                            var _account_current_balance;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-background border border-border rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-text-primary\",\n                                                                children: account.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\",\n                                                                children: account.account_type\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-text-primary\",\n                                                        children: [\n                                                            \"$\",\n                                                            ((_account_current_balance = account.current_balance) === null || _account_current_balance === void 0 ? void 0 : _account_current_balance.toFixed(2)) || '0.00'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    account.institution_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-text-secondary mt-1\",\n                                                        children: account.institution_name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, account.id, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-surface border border-border rounded-xl p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                                        children: \"Recent Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this),\n                                    investmentTransactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 text-text-secondary mx-auto mb-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary\",\n                                                children: \"No investment transactions found.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-border\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Symbol\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Quantity\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Price\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"text-left py-3 px-4 font-medium text-text-secondary\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: investmentTransactions.slice(0, 10).map((transaction)=>{\n                                                        var _transaction_investment_price;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-border hover:bg-surface-elevated\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: new Date(transaction.transaction_date).toLocaleDateString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(transaction.transaction_type === 'investment_buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                        children: transaction.transaction_type === 'investment_buy' ? 'Buy' : 'Sell'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary font-medium\",\n                                                                    children: transaction.investment_symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: transaction.investment_quantity\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        (_transaction_investment_price = transaction.investment_price) === null || _transaction_investment_price === void 0 ? void 0 : _transaction_investment_price.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-3 px-4 text-text-primary font-medium\",\n                                                                    children: [\n                                                                        \"$\",\n                                                                        transaction.amount.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, transaction.id, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_4__.Modal, {\n                                isOpen: showInvestmentForm,\n                                onClose: ()=>setShowInvestmentForm(false),\n                                title: \"Investment Transaction\",\n                                size: \"xl\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvestmentForm__WEBPACK_IMPORTED_MODULE_3__.InvestmentForm, {\n                                    onSubmit: handleInvestmentSubmit,\n                                    loading: submitting,\n                                    compact: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/investments/page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(InvestmentsPage, \"GbZkdDSwNIkUe2WvjyQA6Sg22P8=\");\n_c = InvestmentsPage;\nvar _c;\n$RefreshReg$(_c, \"InvestmentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/investments/page.tsx\n"));

/***/ })

});